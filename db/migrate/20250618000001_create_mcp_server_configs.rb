# frozen_string_literal: true

class CreateMcpServerConfigs < ActiveRecord::Migration[7.1]
  def change
    create_table :mcp_server_configs do |t|
      t.references :link, null: false, foreign_key: true, index: true
      t.string :endpoint_url, null: false
      t.text :description
      t.json :supported_tools, default: []
      t.text :api_documentation
      t.string :health_status, default: 'unknown'
      t.datetime :last_health_check_at
      t.json :server_metadata, default: {}
      t.boolean :is_active, default: true
      t.timestamps

      t.index [:link_id, :is_active]
      t.index :health_status
      t.index :last_health_check_at
    end
  end
end
